{"name": "fuckmp", "version": "1.0.0", "description": "FuckMP - 强大的 Vue3 小程序编译器，支持完整的 Vue3 语法和 SCSS 导入", "keywords": ["vue3", "miniprogram", "wechat", "compiler", "typescript", "scss", "fuckmp"], "author": "Vue3 MiniProgram Compiler Team", "license": "MIT", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "bin": {"fuckmp": "./dist/cli/index.js", "fmp": "./dist/cli/index.js"}, "files": ["dist", "templates", "README.md"], "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "scripts": {"dev": "tsx src/cli/index.ts dev", "build": "tsup", "build:watch": "tsup --watch", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "vitest tests/e2e", "test:watch": "vitest --watch", "test:run": "vitest run", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.js --fix", "format": "prettier --write \"src/**/*.{ts,js,json,md}\"", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "pnpm run clean && pnpm run build", "release": "changeset publish", "cli:create": "tsx src/cli/index.ts create", "cli:build": "tsx src/cli/index.ts build", "cli:analyze": "tsx src/cli/index.ts analyze"}, "dependencies": {"@babel/core": "^7.23.7", "@babel/generator": "^7.23.6", "@babel/parser": "^7.23.6", "@babel/traverse": "^7.23.7", "@babel/types": "^7.23.6", "@vue/compiler-sfc": "^3.4.15", "@vue/reactivity": "^3.5.16", "autoprefixer": "^10.4.16", "chokidar": "^3.5.3", "commander": "^11.1.0", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "magic-string": "^0.30.5", "node-html-parser": "^7.0.1", "picocolors": "^1.0.0", "postcss": "^8.4.33", "sass": "^1.69.7"}, "devDependencies": {"@changesets/cli": "^2.27.1", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.6.7", "@types/babel__traverse": "^7.20.4", "@types/fs-extra": "^11.0.4", "@types/node": "^20.11.5", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "@vitest/coverage-v8": "^1.2.1", "@vitest/ui": "^1.2.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "miniprogram-automator": "^0.12.1", "prettier": "^3.2.4", "rimraf": "^5.0.5", "tsup": "^8.0.1", "tsx": "^4.7.0", "typescript": "^5.6.2", "vitest": "^1.2.1"}, "peerDependencies": {"vue": "^3.4.0"}, "repository": {"type": "git", "url": "https://github.com/vue3-miniprogram/compiler.git"}, "bugs": {"url": "https://github.com/vue3-miniprogram/compiler/issues"}, "homepage": "https://github.com/vue3-miniprogram/compiler#readme", "packageManager": "pnpm@10.8.0+sha512.0e82714d1b5b43c74610193cb20734897c1d00de89d0e18420aebc5977fa13d780a9cb05734624e81ebd81cc876cd464794850641c48b9544326b5622ca29971"}